"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import {
  Wand2,
  Sparkles,
  Download,
  Copy,
  RefreshCw,
  Image as ImageIcon,
  Video,
  FileText,
  Loader2,
  CheckCircle,
  AlertCircle,
  Share2,
  MoreHorizontal,
  Eye,
  Save,
  DownloadCloud
} from "lucide-react";
import { toast } from "sonner";

interface GeneratedAd {
  id: string;
  type: 'text' | 'image' | 'video';
  content: string;
  headline?: string;
  description?: string;
  cta?: string;
  imageUrl?: string;
  videoUrl?: string;
  platform?: string;
  format?: string;
}

export default function GenerateAdsPage() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedAds, setGeneratedAds] = useState<GeneratedAd[]>([]);
  const [previewMode, setPreviewMode] = useState<'content' | 'preview'>('content');
  const [formData, setFormData] = useState({
    productName: "",
    productDescription: "",
    targetAudience: "",
    adType: "text",
    platform: "facebook",
    tone: "professional",
    keywords: "",
    cta: "",
    additionalInfo: ""
  });

  const adTypes = [
    { value: "text", label: "Text Ad", icon: FileText },
    { value: "image", label: "Image Ad", icon: ImageIcon },
    { value: "video", label: "Video Ad", icon: Video }
  ];

  const platforms = [
    { value: "facebook", label: "Facebook" },
    { value: "instagram", label: "Instagram" },
    { value: "google", label: "Google Ads" },
    { value: "linkedin", label: "LinkedIn" },
    { value: "twitter", label: "Twitter/X" },
    { value: "tiktok", label: "TikTok" },
    { value: "youtube", label: "YouTube" }
  ];

  const tones = [
    { value: "professional", label: "Professional" },
    { value: "casual", label: "Casual" },
    { value: "friendly", label: "Friendly" },
    { value: "urgent", label: "Urgent" },
    { value: "humorous", label: "Humorous" },
    { value: "inspirational", label: "Inspirational" }
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const generateAds = async () => {
    if (!formData.productName || !formData.productDescription) {
      toast.error("Please fill in the product name and description");
      return;
    }

    setIsGenerating(true);

    try {
      // Get Firebase auth token
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) {
        toast.error("Please log in to generate ads");
        return;
      }

      const token = await getIdToken(auth.currentUser);

      // Call the ad generation API
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/ad-generation/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify({
          product_name: formData.productName,
          product_description: formData.productDescription,
          target_audience: formData.targetAudience,
          ad_type: formData.adType,
          platform: formData.platform,
          tone: formData.tone,
          keywords: formData.keywords,
          cta: formData.cta,
          additional_info: formData.additionalInfo,
          num_variations: 3
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to generate ads");
      }

      const data = await response.json();

      if (data.success && data.ads) {
        setGeneratedAds(data.ads);
        toast.success(data.message || "Ads generated successfully!");
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      console.error("Ad generation error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to generate ads. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success("Copied to clipboard!");
  };

  const downloadAd = (ad: GeneratedAd, format: 'txt' | 'json' | 'csv' = 'txt') => {
    let content: string;
    let filename: string;
    let mimeType: string;

    const baseFilename = `${ad.headline?.replace(/\s+/g, '_') || 'ad'}_${ad.id}`;

    switch (format) {
      case 'json':
        content = JSON.stringify(ad, null, 2);
        filename = `${baseFilename}.json`;
        mimeType = 'application/json';
        break;
      case 'csv':
        content = `Headline,Description,CTA,Content,Platform,Type\n"${ad.headline}","${ad.description}","${ad.cta}","${ad.content?.replace(/"/g, '""')}","${ad.platform}","${ad.type}"`;
        filename = `${baseFilename}.csv`;
        mimeType = 'text/csv';
        break;
      default:
        content = `HEADLINE: ${ad.headline}\n\nDESCRIPTION: ${ad.description}\n\nCTA: ${ad.cta}\n\nFULL CONTENT:\n${ad.content}\n\nPLATFORM: ${ad.platform}\nTYPE: ${ad.type}`;
        filename = `${baseFilename}.txt`;
        mimeType = 'text/plain';
    }

    const element = document.createElement("a");
    const file = new Blob([content], { type: mimeType });
    element.href = URL.createObjectURL(file);
    element.download = filename;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    toast.success(`Ad downloaded as ${format.toUpperCase()}!`);
  };

  const downloadAllAds = (format: 'txt' | 'json' | 'csv' = 'json') => {
    if (generatedAds.length === 0) {
      toast.error("No ads to download");
      return;
    }

    let content: string;
    let filename: string;
    let mimeType: string;

    switch (format) {
      case 'json':
        content = JSON.stringify(generatedAds, null, 2);
        filename = `generated_ads_${Date.now()}.json`;
        mimeType = 'application/json';
        break;
      case 'csv':
        const headers = 'Headline,Description,CTA,Content,Platform,Type\n';
        const rows = generatedAds.map(ad =>
          `"${ad.headline}","${ad.description}","${ad.cta}","${ad.content?.replace(/"/g, '""')}","${ad.platform}","${ad.type}"`
        ).join('\n');
        content = headers + rows;
        filename = `generated_ads_${Date.now()}.csv`;
        mimeType = 'text/csv';
        break;
      default:
        content = generatedAds.map((ad, index) =>
          `=== AD ${index + 1} ===\nHEADLINE: ${ad.headline}\nDESCRIPTION: ${ad.description}\nCTA: ${ad.cta}\nFULL CONTENT:\n${ad.content}\nPLATFORM: ${ad.platform}\nTYPE: ${ad.type}\n\n`
        ).join('');
        filename = `generated_ads_${Date.now()}.txt`;
        mimeType = 'text/plain';
    }

    const element = document.createElement("a");
    const file = new Blob([content], { type: mimeType });
    element.href = URL.createObjectURL(file);
    element.download = filename;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    toast.success(`All ads downloaded as ${format.toUpperCase()}!`);
  };

  const shareAd = async (ad: GeneratedAd) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: ad.headline,
          text: ad.content,
          url: window.location.href
        });
        toast.success("Ad shared successfully!");
      } catch (error) {
        if ((error as Error).name !== 'AbortError') {
          copyToClipboard(ad.content);
        }
      }
    } else {
      copyToClipboard(ad.content);
    }
  };

  const AdPreview = ({ ad }: { ad: GeneratedAd }) => {
    const platformStyles = {
      facebook: {
        container: "bg-white border border-gray-200 rounded-lg p-4 max-w-md",
        header: "flex items-center gap-3 mb-3",
        avatar: "w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold",
        name: "font-semibold text-gray-900",
        time: "text-gray-500 text-sm",
        content: "text-gray-800 mb-3",
        cta: "bg-blue-600 text-white px-4 py-2 rounded-md font-medium text-sm"
      },
      instagram: {
        container: "bg-white border border-gray-200 rounded-lg overflow-hidden max-w-md",
        header: "flex items-center gap-3 p-4",
        avatar: "w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm",
        name: "font-semibold text-gray-900 text-sm",
        content: "px-4 pb-4 text-gray-800 text-sm",
        cta: "text-blue-600 font-medium"
      },
      google: {
        container: "bg-white border border-gray-200 rounded p-4 max-w-md",
        headline: "text-blue-600 text-lg font-medium hover:underline cursor-pointer",
        url: "text-green-700 text-sm",
        description: "text-gray-700 text-sm mt-1"
      },
      linkedin: {
        container: "bg-white border border-gray-200 rounded-lg p-4 max-w-md",
        header: "flex items-center gap-3 mb-3",
        avatar: "w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center text-white font-bold",
        name: "font-semibold text-gray-900",
        title: "text-gray-600 text-sm",
        content: "text-gray-800 mb-3",
        cta: "bg-blue-700 text-white px-4 py-2 rounded-md font-medium text-sm"
      }
    };

    const style = platformStyles[ad.platform as keyof typeof platformStyles] || platformStyles.facebook;

    if (ad.platform === 'google') {
      return (
        <div className={style.container}>
          <div className={style.headline}>{ad.headline}</div>
          <div className={style.url}>www.example.com</div>
          <div className={style.description}>{ad.description}</div>
        </div>
      );
    }

    return (
      <div className={style.container}>
        <div className={style.header}>
          <div className={style.avatar}>B</div>
          <div>
            <div className={style.name}>Your Brand</div>
            {ad.platform === 'linkedin' && <div className={style.title}>Company</div>}
            {ad.platform !== 'linkedin' && <div className="text-gray-500 text-sm">Sponsored</div>}
          </div>
        </div>
        <div className={style.content}>
          {ad.content}
        </div>
        <button className={style.cta}>
          {ad.cta}
        </button>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50/30">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center gap-4">
              <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-blue-600 text-white">
                <Wand2 className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 tracking-tight">
                  Generate Ads
                </h1>
                <p className="mt-2 text-sm text-gray-600">
                  Create compelling ads for your products using AI-powered generation
                </p>
              </div>
              <Badge variant="secondary" className="ml-auto">
                BETA
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-6 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Input Form */}
            <Card className="h-fit">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-purple-600" />
                  Ad Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Product Information */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="productName">Product Name *</Label>
                    <Input
                      id="productName"
                      placeholder="Enter your product name"
                      value={formData.productName}
                      onChange={(e) => handleInputChange("productName", e.target.value)}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="productDescription">Product Description *</Label>
                    <Textarea
                      id="productDescription"
                      placeholder="Describe your product and its key benefits"
                      value={formData.productDescription}
                      onChange={(e) => handleInputChange("productDescription", e.target.value)}
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="targetAudience">Target Audience</Label>
                    <Input
                      id="targetAudience"
                      placeholder="e.g., Small business owners, Tech enthusiasts"
                      value={formData.targetAudience}
                      onChange={(e) => handleInputChange("targetAudience", e.target.value)}
                    />
                  </div>
                </div>

                <Separator />

                {/* Ad Configuration */}
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Ad Type</Label>
                      <Select value={formData.adType} onValueChange={(value) => handleInputChange("adType", value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {adTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              <div className="flex items-center gap-2">
                                <type.icon className="h-4 w-4" />
                                {type.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Platform</Label>
                      <Select value={formData.platform} onValueChange={(value) => handleInputChange("platform", value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {platforms.map((platform) => (
                            <SelectItem key={platform.value} value={platform.value}>
                              {platform.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label>Tone</Label>
                    <Select value={formData.tone} onValueChange={(value) => handleInputChange("tone", value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {tones.map((tone) => (
                          <SelectItem key={tone.value} value={tone.value}>
                            {tone.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="keywords">Keywords</Label>
                    <Input
                      id="keywords"
                      placeholder="Enter relevant keywords (comma separated)"
                      value={formData.keywords}
                      onChange={(e) => handleInputChange("keywords", e.target.value)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="cta">Call to Action</Label>
                    <Input
                      id="cta"
                      placeholder="e.g., Learn More, Get Started, Buy Now"
                      value={formData.cta}
                      onChange={(e) => handleInputChange("cta", e.target.value)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="additionalInfo">Additional Information</Label>
                    <Textarea
                      id="additionalInfo"
                      placeholder="Any additional context or requirements"
                      value={formData.additionalInfo}
                      onChange={(e) => handleInputChange("additionalInfo", e.target.value)}
                      rows={2}
                    />
                  </div>
                </div>

                <Button 
                  onClick={generateAds} 
                  disabled={isGenerating || !formData.productName || !formData.productDescription}
                  className="w-full"
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Generating Ads...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4 mr-2" />
                      Generate Ads
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Generated Ads */}
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Generated Ads</h2>
                <div className="flex items-center gap-3">
                  {generatedAds.length > 0 && (
                    <>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <DownloadCloud className="h-4 w-4 mr-2" />
                            Export All
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem onClick={() => downloadAllAds('txt')}>
                            <FileText className="h-4 w-4 mr-2" />
                            Download as TXT
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => downloadAllAds('json')}>
                            <FileText className="h-4 w-4 mr-2" />
                            Download as JSON
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => downloadAllAds('csv')}>
                            <FileText className="h-4 w-4 mr-2" />
                            Download as CSV
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                      <div className="flex items-center gap-2">
                        <Button
                          variant={previewMode === 'content' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setPreviewMode('content')}
                        >
                          <FileText className="h-4 w-4 mr-1" />
                          Content
                        </Button>
                        <Button
                          variant={previewMode === 'preview' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setPreviewMode('preview')}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Preview
                        </Button>
                      </div>
                      <Badge variant="outline" className="text-green-600 border-green-200">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {generatedAds.length} ads generated
                      </Badge>
                    </>
                  )}
                </div>
              </div>

              {generatedAds.length === 0 && !isGenerating && (
                <Card className="border-dashed border-2 border-gray-300">
                  <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                    <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No ads generated yet</h3>
                    <p className="text-gray-600 mb-4">Fill in the form and click "Generate Ads" to create your first ad</p>
                  </CardContent>
                </Card>
              )}

              {isGenerating && (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                    <Loader2 className="h-12 w-12 text-purple-600 animate-spin mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Generating your ads...</h3>
                    <p className="text-gray-600">This may take a few moments</p>
                  </CardContent>
                </Card>
              )}

              {generatedAds.map((ad) => (
                <Card key={ad.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{ad.headline}</CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{ad.platform}</Badge>
                        <Badge variant="secondary">{ad.type}</Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {previewMode === 'content' ? (
                        <div className="bg-gray-50 rounded-lg p-4">
                          <pre className="whitespace-pre-wrap text-sm text-gray-800 font-medium">
                            {ad.content}
                          </pre>
                        </div>
                      ) : (
                        <div className="flex justify-center p-4 bg-gray-50 rounded-lg">
                          <AdPreview ad={ad} />
                        </div>
                      )}
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(ad.content)}
                          >
                            <Copy className="h-4 w-4 mr-1" />
                            Copy
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => shareAd(ad)}
                          >
                            <Share2 className="h-4 w-4 mr-1" />
                            Share
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="sm">
                                <Download className="h-4 w-4 mr-1" />
                                Download
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem onClick={() => downloadAd(ad, 'txt')}>
                                <FileText className="h-4 w-4 mr-2" />
                                Download as TXT
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => downloadAd(ad, 'json')}>
                                <FileText className="h-4 w-4 mr-2" />
                                Download as JSON
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => downloadAd(ad, 'csv')}>
                                <FileText className="h-4 w-4 mr-2" />
                                Download as CSV
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={generateAds}
                          className="text-gray-500 hover:text-gray-700"
                        >
                          <RefreshCw className="h-4 w-4 mr-1" />
                          Regenerate All
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
