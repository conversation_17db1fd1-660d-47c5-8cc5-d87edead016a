"""
AdMesh Ad Generation API
Provides AI-powered ad generation capabilities for brands
"""

import os
import json
import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from openai import OpenAI
from auth.deps import require_role
from config.database import get_db
from google.cloud import firestore

# Configure logging
logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter()

# Initialize OpenAI client
openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

class AdGenerationRequest(BaseModel):
    product_name: str
    product_description: str
    target_audience: Optional[str] = None
    ad_type: str = "text"  # text, image, video
    platform: str = "facebook"
    tone: str = "professional"
    keywords: Optional[str] = None
    cta: Optional[str] = None
    additional_info: Optional[str] = None
    num_variations: int = 3

class GeneratedAd(BaseModel):
    id: str
    type: str
    content: str
    headline: Optional[str] = None
    description: Optional[str] = None
    cta: Optional[str] = None
    platform: str
    format: str

class AdGenerationResponse(BaseModel):
    success: bool
    ads: List[GeneratedAd]
    message: Optional[str] = None

def create_ad_generation_prompt(request: AdGenerationRequest) -> str:
    """Create a detailed prompt for OpenAI to generate ads"""
    
    platform_specs = {
        "facebook": {
            "headline_limit": 40,
            "description_limit": 125,
            "best_practices": "Use engaging visuals, clear value propositions, and strong CTAs"
        },
        "instagram": {
            "headline_limit": 30,
            "description_limit": 125,
            "best_practices": "Focus on visual storytelling, use hashtags, keep text minimal"
        },
        "google": {
            "headline_limit": 30,
            "description_limit": 90,
            "best_practices": "Include keywords, be specific about benefits, use ad extensions"
        },
        "linkedin": {
            "headline_limit": 150,
            "description_limit": 600,
            "best_practices": "Professional tone, focus on business value, target decision makers"
        },
        "twitter": {
            "headline_limit": 280,
            "description_limit": 280,
            "best_practices": "Concise messaging, use trending hashtags, encourage engagement"
        },
        "tiktok": {
            "headline_limit": 100,
            "description_limit": 100,
            "best_practices": "Trendy language, video-first content, authentic feel"
        },
        "youtube": {
            "headline_limit": 100,
            "description_limit": 5000,
            "best_practices": "Compelling thumbnails, clear value in first 5 seconds, strong hooks"
        }
    }
    
    platform_info = platform_specs.get(request.platform, platform_specs["facebook"])
    
    prompt = f"""
You are an expert advertising copywriter specializing in creating high-converting ads for {request.platform.title()}. 

Create {request.num_variations} different ad variations for the following product:

PRODUCT INFORMATION:
- Product Name: {request.product_name}
- Description: {request.product_description}
- Target Audience: {request.target_audience or "General audience"}
- Keywords: {request.keywords or "Not specified"}
- Desired CTA: {request.cta or "Learn More"}
- Additional Context: {request.additional_info or "None"}

PLATFORM REQUIREMENTS ({request.platform.title()}):
- Headline limit: {platform_info['headline_limit']} characters
- Description limit: {platform_info['description_limit']} characters
- Best practices: {platform_info['best_practices']}

TONE: {request.tone.title()}

AD TYPE: {request.ad_type.title()}

INSTRUCTIONS:
1. Create {request.num_variations} unique ad variations
2. Each ad should have a compelling headline, engaging description, and strong call-to-action
3. Use the specified tone throughout
4. Incorporate the target audience and keywords naturally
5. Follow platform-specific best practices and character limits
6. Make each variation distinctly different in approach (emotional, logical, urgency-based, etc.)
7. Include relevant emojis where appropriate for the platform
8. Ensure ads are compliant with platform advertising policies

OUTPUT FORMAT:
Return a JSON array with {request.num_variations} ad objects, each containing:
{{
  "headline": "Compelling headline under {platform_info['headline_limit']} chars",
  "description": "Engaging description under {platform_info['description_limit']} chars", 
  "cta": "Strong call-to-action",
  "content": "Full ad copy combining headline and description",
  "approach": "Brief description of the approach used (e.g., 'emotional appeal', 'logical benefits', 'urgency-driven')"
}}

Only return valid JSON - no additional text or formatting.
"""
    
    return prompt

def call_openai_for_ads(prompt: str) -> str:
    """Call OpenAI API to generate ads"""
    try:
        response = openai_client.chat.completions.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert advertising copywriter who creates high-converting ads. Always respond with valid JSON only."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            temperature=0.8,
            max_tokens=2000
        )

        return response.choices[0].message.content

    except Exception as e:
        logger.error(f"OpenAI API error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate ads: {str(e)}")

def parse_openai_response(response_text: str, request: AdGenerationRequest) -> List[GeneratedAd]:
    """Parse OpenAI response and create GeneratedAd objects"""
    try:
        # Clean the response text
        cleaned_response = response_text.strip()
        if cleaned_response.startswith("```json"):
            cleaned_response = cleaned_response[7:]
        if cleaned_response.endswith("```"):
            cleaned_response = cleaned_response[:-3]
        
        ads_data = json.loads(cleaned_response)
        
        generated_ads = []
        for i, ad_data in enumerate(ads_data):
            generated_ad = GeneratedAd(
                id=f"ad_{i+1}",
                type=request.ad_type,
                content=ad_data.get("content", ""),
                headline=ad_data.get("headline", ""),
                description=ad_data.get("description", ""),
                cta=ad_data.get("cta", request.cta or "Learn More"),
                platform=request.platform,
                format=request.ad_type
            )
            generated_ads.append(generated_ad)
        
        return generated_ads
        
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse OpenAI response as JSON: {str(e)}")
        logger.error(f"Response text: {response_text}")
        raise HTTPException(status_code=500, detail="Failed to parse generated ads")
    except Exception as e:
        logger.error(f"Error processing OpenAI response: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process generated ads")

def save_ad_generation_session(brand_id: str, request: AdGenerationRequest, ads: List[GeneratedAd]):
    """Save ad generation session to Firestore for analytics"""
    try:
        db = get_db()
        session_data = {
            "brand_id": brand_id,
            "request": request.model_dump(),
            "generated_ads": [ad.model_dump() for ad in ads],
            "created_at": firestore.SERVER_TIMESTAMP,
            "num_ads_generated": len(ads)
        }
        
        db.collection("ad_generation_sessions").add(session_data)
        logger.info(f"Saved ad generation session for brand {brand_id}")
        
    except Exception as e:
        logger.error(f"Failed to save ad generation session: {str(e)}")
        # Don't raise exception here as it's not critical for the main functionality

@router.post("/generate", response_model=AdGenerationResponse)
async def generate_ads(
    request: AdGenerationRequest,
    user_data = Depends(require_role("brand"))
):
    """Generate AI-powered ads for a brand's product"""
    
    brand_id = user_data["uid"]
    logger.info(f"Ad generation request from brand {brand_id}: {request.product_name}")
    
    try:
        # Validate request
        if not request.product_name or not request.product_description:
            raise HTTPException(
                status_code=400, 
                detail="Product name and description are required"
            )
        
        # Limit number of variations
        if request.num_variations > 5:
            request.num_variations = 5
        elif request.num_variations < 1:
            request.num_variations = 1
        
        # Create prompt for OpenAI
        prompt = create_ad_generation_prompt(request)
        
        # Call OpenAI API
        openai_response = call_openai_for_ads(prompt)
        
        # Parse response into GeneratedAd objects
        generated_ads = parse_openai_response(openai_response, request)
        
        # Save session for analytics (non-blocking)
        try:
            save_ad_generation_session(brand_id, request, generated_ads)
        except Exception as e:
            logger.warning(f"Failed to save session data: {str(e)}")
        
        logger.info(f"Successfully generated {len(generated_ads)} ads for brand {brand_id}")
        
        return AdGenerationResponse(
            success=True,
            ads=generated_ads,
            message=f"Successfully generated {len(generated_ads)} ad variations"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in ad generation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while generating ads"
        )

@router.get("/history")
async def get_ad_generation_history(
    user_data = Depends(require_role("brand")),
    limit: int = 10
):
    """Get ad generation history for a brand"""
    
    brand_id = user_data["uid"]
    
    try:
        db = get_db()
        sessions = (
            db.collection("ad_generation_sessions")
            .where("brand_id", "==", brand_id)
            .order_by("created_at", direction=firestore.Query.DESCENDING)
            .limit(limit)
            .stream()
        )
        
        history = []
        for session in sessions:
            session_data = session.to_dict()
            session_data["id"] = session.id
            history.append(session_data)
        
        return {"success": True, "history": history}
        
    except Exception as e:
        logger.error(f"Failed to get ad generation history: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve ad generation history")
